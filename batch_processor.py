import os
import glob
import zipfile
import tempfile
import shutil
import pandas as pd

def extract_zip_files(input_folder):
    temp_dir = tempfile.mkdtemp(prefix="blast_hittrax_")
    for z in glob.glob(os.path.join(input_folder, "*.zip")):
        with zipfile.ZipFile(z, 'r') as archive:
            archive.extractall(temp_dir)
    return temp_dir

def extract_athlete_from_filename(filepath):
    stem = os.path.splitext(os.path.basename(filepath))[0]
    stem = stem.replace("Metrics - ", "").replace("Metrics", "").strip()
    return stem.title()

def parse_blast(filepath):
    with open(filepath, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    for i, line in enumerate(lines):
        if line.lower().startswith("date,equipment"):
            header_idx = i
            break
    else:
        raise ValueError(f"No Blast header in {filepath}")
    df = pd.read_csv(filepath, skiprows=header_idx)
    df['timestamp'] = pd.to_datetime(
        df['Date'].astype(str).str.strip(),
        format="%b %d, %Y %I:%M:%S %p",
        errors="coerce"
    )
    df['athlete']    = extract_athlete_from_filename(filepath)
    df['blast_file'] = os.path.basename(filepath)
    return df

def parse_hittrax(filepath):
    df = pd.read_csv(filepath)
    df.rename(columns={c: c.strip():c.strip() for c in df.columns}, inplace=True)

    date_col = next((c for c in df.columns if c.strip().lower().startswith("date")), None)
    if not date_col:
        raise ValueError(f"No Date column in {filepath}")
    df['timestamp'] = pd.to_datetime(
        df[date_col].astype(str).str.strip(),
        format="%m/%d/%Y %H:%M:%S.%f",
        errors="coerce"
    )

    user_col = next((c for c in df.columns if c.strip().lower().startswith("user")), None)
    df['athlete'] = df[user_col].astype(str).str.strip().str.title() if user_col else "Unknown"

    velo_col = next((c for c in df.columns if "velo" in c.lower() and "exit" not in c.lower()), None)
    df['has_contact'] = df[velo_col].gt(0) if velo_col else False

    df['hittrax_file'] = os.path.basename(filepath)
    return df

def merge_athlete_data(blast_df, hit_df, max_seconds=5):
    hit_ct = hit_df[hit_df['has_contact']].sort_values('timestamp')
    blast_sorted = blast_df.sort_values('timestamp')
    merged = pd.merge_asof(
        hit_ct,
        blast_sorted,
        on='timestamp',
        by='athlete',
        tolerance=pd.Timedelta(seconds=max_seconds),
        direction='nearest',
        suffixes=('_hit','_blast')
    )
    merged['Match_Type']  = merged['blast_file'].notna().map({True:'Matched',False:'HitTrax_Only_Contact'})
    merged['Time_Diff_s'] = merged['timestamp_hit'].subtract(merged['timestamp_blast']).abs().dt.total_seconds()
    merged['Confidence']  = pd.cut(merged['Time_Diff_s'], bins=[-1,3,max_seconds,float('inf')], labels=['High','OK','Unmatched'])

    # catch any Blast-only
    matched = merged[merged['blast_file'].notna()]
    unmatched_b = blast_sorted.drop(matched.index, errors='ignore')
    if not unmatched_b.empty:
        ub = unmatched_b.copy()
        for c in hit_ct.columns:
            if c not in ['athlete','timestamp','has_contact','hittrax_file']:
                ub[c] = pd.NA
        ub['Match_Type']  = 'Blast_Only'
        ub['Time_Diff_s'] = pd.NA
        ub['Confidence']  = 'Unmatched'
        merged = pd.concat([merged, ub], ignore_index=True)
    return merged

def dedupe(df):
    return df.drop_duplicates(subset=['athlete','timestamp'], keep='first')

def clean_merged_df(df):
    # 1) Trim whitespace
    for col in df.select_dtypes(['object']):
        df[col] = df[col].str.strip()
    # 2) Normalize athlete
    df['athlete'] = df['athlete'].str.title()
    # 3) Cast numeric
    num_keys = ['velocity','speed','angle','distance','score','time_diff']
    for c in df.columns:
        if any(k in c.lower() for k in num_keys):
            df[c] = pd.to_numeric(df[c], errors='coerce')
    # 4) Drop bad
    bad_ts = df['timestamp'].isna()
    bad_ev = (df['Match_Type']=='Matched') & df.get('Blast_Exit_Velocity_mph', pd.Series()).isna()
    df = df[~(bad_ts|bad_ev)]
    # 5) Filter exit velocity
    ev = df.get('Blast_Exit_Velocity_mph')
    if ev is not None:
        df = df[(ev.between(20,120)) | (ev.isna())]
    # 6) Normalize equipment
    df['Blast_Equipment'] = df['Blast_Equipment'].replace({
        'softball bat':'Softball',
        'baseball bat':'Baseball'
    })
    return df

def batch_process(input_folder, output_folder=None):
    if output_folder is None:
        output_folder = os.path.join(input_folder, "merged_results")
    os.makedirs(output_folder, exist_ok=True)

    extra_dir = extract_zip_files(input_folder)
    paths = [input_folder, extra_dir]

    all_csvs = []
    for p in paths:
        found = glob.glob(os.path.join(p, "*.csv"))
        print(f"🔍 Looking in {p}, found {len(found)} CSVs")
        all_csvs += found

    blasts, hits = [], []
    for f in all_csvs:
        head = ""
        with open(f, 'r', encoding='utf-8', errors='ignore') as fh:
            head = fh.read(500).lower()
        print(f"  • Examining {os.path.basename(f)}")
        if 'date,equipment' in head:
            print("    → classified as Blast")
            blasts.append(parse_blast(f))
        else:
            try:
                df0 = pd.read_csv(f, nrows=0)
            except pd.errors.EmptyDataError:
                print("    ⚠️ empty or malformed, skipping")
                continue
            cols = [c.strip().lower() for c in df0.columns]
            if any(c.startswith('date') for c in cols) and any(c.startswith('user') for c in cols):
                print("    → classified as HitTrax")
                hits.append(parse_hittrax(f))
            else:
                print("    ⚠️ neither Blast nor HitTrax, skipping")

    blasts_df = pd.concat(blasts, ignore_index=True) if blasts else pd.DataFrame()
    hits_df   = pd.concat(hits,   ignore_index=True) if hits   else pd.DataFrame()

    results = []
    athletes = set(blasts_df.get('athlete', [])) | set(hits_df.get('athlete', []))
    for ath in athletes:
        bdf = blasts_df[blasts_df['athlete']==ath] if not blasts_df.empty else pd.DataFrame()
        hdf = hits_df  [hits_df['athlete']==ath] if not hits_df.empty   else pd.DataFrame()
        if bdf.empty and hdf.empty:
            continue
        merged = merge_athlete_data(bdf, hdf)
        merged = dedupe(merged)
        merged = clean_merged_df(merged)
        out = os.path.join(output_folder, f"{ath.replace(' ', '_')}_complete.csv")
        merged.to_csv(out, index=False)
        print(f"  ✅ {ath}: {len(merged)} rows → {out}")
        results.append((ath, len(merged), out))

    if results:
        masters = pd.concat([pd.read_csv(r[2]) for r in results], ignore_index=True)
        masters = dedupe(masters)
        masters = clean_merged_df(masters)
        mf = os.path.join(output_folder, "ALL_ATHLETES_complete.csv")
        masters.to_csv(mf, index=False)
        print(f"\n🎉 Master file → {mf}")
    else:
        print("⚠️ No data written; please check your input CSVs.")

    shutil.rmtree(extra_dir)
    return results

if __name__ == "__main__":
    folder = r"C:\Users\<USER>\OneDrive\Desktop\BlastHittrax_MultiAthlete"
    batch_process(folder)
